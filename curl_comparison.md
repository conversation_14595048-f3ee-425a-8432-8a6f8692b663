# curl命令对比分析

## 你的原始curl命令分析

```bash
curl -k -i --raw -o 0.dat \
  -H "host: abcyun.cn" \
  -H "x-abc-ts: 1751176339997" \
  -H "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540512) XWEB/13871 Flue" \
  -H "x-weapp-token: eyJhbGciOiJIUzI1NiJ9.eyJjaGFpbklkIjoiZmZmZmZmZmYwMDAwMDAwMDM0N2VmZDM2NjJkZTQwMDEiLCJhcHBJZCI6Ind4MzE1NjI0MWI0ZmM0YjdkMSIsImFwcFR5cGUiOjAsIm9wZW5JZCI6Im9GUmJ6NXRVTUN1c0hROGlJM2l3anVCTlpxR00iLCJ3eFVzZXJJZCI6IjM4MjAzMDEwNzEzMzI4ODQ0ODAiLCJ1bmlvbklkIjoib3VjWWg1Z1JXaExwNFpYWHZGWXpkaXpUaXQxVSIsImlzUENQcmV2aWV3IjowLCJpc0NoYWluRXhwaXJlZCI6MCwiaXNDaGFpbkVkaXRpb25NY0Rpc2FibGUiOjB9.56vvGHAU34EN6uLSDTJ0Eck0DXkhmZEn0uQaeYnxM0g" \
  -H "accept: application/json, text/plain, */*" \
  -H "x-abc-sign: 1431f8376cfc002947bc95ef3327b3ba" \
  -H "weapp-env: prod" \
  -H "x-aua: ABCWeClinic/weh5-v2025.26.01" \
  -H "weapp-app-vn: weh5-v2025.26.01" \
  -H "weapp-app-id: wx3156241b4fc4b7d1" \
  -H "sec-fetch-site: same-origin" \
  -H "sec-fetch-mode: cors" \
  -H "sec-fetch-dest: empty" \
  -H "referer: https://abcyun.cn/mp/2182/doctor-detail?employeeId=ffffffff00000000347efd26a343c000&clinicId=ffffffff00000000347efd3662de4002&registrationDate=2025-06-29&tab=outpatient_registration" \
  -H "accept-encoding: gzip, deflate, br" \
  -H "accept-language: zh-CN,zh;q=0.9" \
  -H "priority: u=1, i" \
  -H "cookie: uuid=f9c5231af4e74e8599038d1a5b4de287; grayflag=" \
  "https://abcyun.cn/api-weapp/registration/chain/doctor/ffffffff00000000347efd26a343c000/shifts?t=1751176339997&clinicId=ffffffff00000000347efd3662de4002&registrationType=0&userGps="
```

## 关键发现

### 1. 时间戳同步 ✅
- `x-abc-ts: 1751176339997`
- URL参数 `t=1751176339997`
- **两者必须相同** ✅

### 2. 认证信息 ⚠️
- `x-weapp-token`: 这是JWT token，可能会过期
- `x-abc-sign`: 这是API签名，可能与请求内容相关
- `cookie`: 包含用户会话信息

### 3. 可能的问题

#### A. Token过期
你的`x-weapp-token`可能已经过期。JWT token通常有时效性。

#### B. 签名验证失败
`x-abc-sign: 1431f8376cfc002947bc95ef3327b3ba` 可能是基于以下内容生成的：
- 请求URL
- 请求参数
- 时间戳
- 其他密钥

#### C. 时间戳过期
虽然我们动态生成时间戳，但服务器可能有时间窗口限制。

## 解决方案

### 方案1: 获取最新的curl命令
1. 重新打开微信小程序
2. 进入医生预约页面
3. 使用开发者工具抓取最新的网络请求
4. 复制最新的curl命令

### 方案2: 分析签名生成规律
如果你能提供多个不同时间的curl命令，我可以分析签名的生成规律。

### 方案3: 使用代理抓包
使用Fiddler或Charles等工具，实时抓取手机上的请求。

## 测试建议

1. **首先测试原始curl命令**
   ```bash
   # 直接运行你的原始curl命令，看是否还能正常工作
   curl -k -i --raw -o test.dat [你的完整curl命令]
   ```

2. **如果原始命令失败**
   说明认证信息确实过期了，需要获取新的。

3. **如果原始命令成功**
   说明我们的Go代码中还有其他差异需要修复。

## 下一步

请提供以下信息之一：
1. 最新的curl命令（重新抓取的）
2. 原始curl命令的测试结果
3. 多个不同时间点的curl命令（用于分析签名规律）

这样我就能帮你进一步调试和修复认证问题。
