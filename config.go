package main

import "time"

// 获取配置
func getConfig() *Config {
	return &Config{
		// API配置
		BaseURL:  "https://abcyun.cn",
		DoctorID: "ffffffff00000000347efd26a343c000",
		ClinicID: "ffffffff00000000347efd3662de4002",

		// 请求头配置 - 基于最新curl命令更新
		Headers: map[string]string{
			"Connection":      "keep-alive",
			"x-weapp-token":   "eyJhbGciOiJIUzI1NiJ9.eyJjaGFpbklkIjoiZmZmZmZmZmYwMDAwMDAwMDM0N2VmZDM2NjJkZTQwMDEiLCJwYXRpZW50SWQiOiJmZmZmZmZmZjAwMDAwMDAwMzQ4NTFjNTczNGRkMDAwMCIsImFwcElkIjoid3gzMTU2MjQxYjRmYzRiN2QxIiwiYXBwVHlwZSI6MCwib3BlbklkIjoib0ZSYno1dFVNQ3VzSFE4aUkzaXdqdUJOWnFHTSIsInd4VXNlcklkIjoiMzgyMDMwMTA3MTMzMjg4NDQ4MCIsInVuaW9uSWQiOiJvdWNZaDVnUldoTHA0WlhYdkZZemRpelRpdDFVIiwiaXNQQ1ByZXZpZXciOjAsImlzQ2hhaW5FeHBpcmVkIjowLCJpc0NoYWluRWRpdGlvbk1jRGlzYWJsZSI6MH0.22Et0VUrCiSj5c7vm06qGq5siuz23m4wYccOHZ9gHks",
			"Accept":          "application/json, text/plain, */*",
			"Sec-Fetch-Site":  "same-origin",
			"X-ABC-Sign":      "785485f303260fa625381e148a4d2e22",
			"X-ABC-Ts":        "1751179074189", // 这个会被动态更新
			"weapp-env":       "prod",
			"x-aua":           "ABCWeClinic/weh5-v2025.26.01",
			"weapp-app-vn":    "weh5-v2025.26.01",
			"weapp-app-id":    "wx3156241b4fc4b7d1",
			"sec-fetch-site":  "same-origin",
			"sec-fetch-mode":  "cors",
			"sec-fetch-dest":  "empty",
			"referer":         "https://abcyun.cn/mp/2182/doctor-detail?employeeId=ffffffff00000000347efd26a343c000&clinicId=ffffffff00000000347efd3662de4002&registrationDate=2025-06-29&tab=outpatient_registration",
			"accept-encoding": "gzip, deflate, br",
			"accept-language": "zh-CN,zh;q=0.9",
			"priority":        "u=1, i",
			"cookie":          "uuid=f9c5231af4e74e8599038d1a5b4de287; grayflag=",
		},

		// 监控配置
		MonitorDates:  []string{"2025-06-30", "2025-07-01"},
		CheckInterval: 5 * time.Second,

		// WxPusher配置 - 需要用户自己配置
		WxPusherToken: "AT_gUx71Mk7GLwDDxpCUVzvtmQ3je5lxrXa", // 需要替换为实际的token
		WxPusherUID:   "UID_j81tld2n1Qy3gdX6cTFDryVuLITy",    // 需要替换为实际的UID
	}
}
