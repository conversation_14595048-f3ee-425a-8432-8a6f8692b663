package main

import "time"

// 获取配置
func getConfig() *Config {
	return &Config{
		// API配置
		BaseURL:  "https://abcyun.cn",
		DoctorID: "ffffffff00000000347efd26a343c000",
		ClinicID: "ffffffff00000000347efd3662de4002",

		// 请求头配置 - 基于最新curl命令更新
		Headers: map[string]string{
			"Connection":      "keep-alive",
			"x-weapp-token":   "eyJhbGciOiJIUzI1NiJ9.eyJjaGFpbklkIjoiZmZmZmZmZmYwMDAwMDAwMDM0N2VmZDM2NjJkZTQwMDEiLCJwYXRpZW50SWQiOiJmZmZmZmZmZjAwMDAwMDAwMzQ4NTFjNTczNGRkMDAwMCIsImFwcElkIjoid3gzMTU2MjQxYjRmYzRiN2QxIiwiYXBwVHlwZSI6MCwib3BlbklkIjoib0ZSYno1dFVNQ3VzSFE4aUkzaXdqdUJOWnFHTSIsInd4VXNlcklkIjoiMzgyMDMwMTA3MTMzMjg4NDQ4MCIsInVuaW9uSWQiOiJvdWNZaDVnUldoTHA0WlhYdkZZemRpelRpdDFVIiwiaXNQQ1ByZXZpZXciOjAsImlzQ2hhaW5FeHBpcmVkIjowLCJpc0NoYWluRWRpdGlvbk1jRGlzYWJsZSI6MH0.22Et0VUrCiSj5c7vm06qGq5siuz23m4wYccOHZ9gHks",
			"Accept":          "application/json, text/plain, */*",
			"Sec-Fetch-Site":  "same-origin",
			"X-ABC-Sign":      "652e18924a1c7ad2148ff2aaffe48d44", // 可重放的签名
			"X-ABC-Ts":        "1751179074189", // 这个会被动态更新
			"Accept-Language": "zh-C<PERSON>,zh-<PERSON>;q=0.9",
			"Sec-Fetch-Mode":  "cors",
			"Cookie":          "grayflag=; uuid=5368cbbb3e97458aa5201621902a0008",
			"User-Agent":      "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.60(0x18003c30) NetType/WIFI Language/zh_CN",
			"weapp-env":       "prod",
			"weapp-app-id":    "wx3156241b4fc4b7d1",
			"Referer":         "https://abcyun.cn/mp/2182/doctor-detail?employeeId=ffffffff00000000347efd26a343c000&clinicId=ffffffff00000000347efd3662de4002&registrationDate=2025-06-30&tab=outpatient_registration",
			"X-AUA":           "ABCWeClinic/weh5-v2025.26.01",
			"Sec-Fetch-Dest":  "empty",
			"weapp-app-vn":    "weh5-v2025.26.01",
		},

		// 监控配置
		MonitorDates:  []string{"2025-06-30", "2025-07-01"},
		CheckInterval: 5 * time.Second,

		// WxPusher配置 - 需要用户自己配置
		WxPusherToken: "AT_gUx71Mk7GLwDDxpCUVzvtmQ3je5lxrXa", // 需要替换为实际的token
		WxPusherUID:   "UID_j81tld2n1Qy3gdX6cTFDryVuLITy",    // 需要替换为实际的UID
	}
}
