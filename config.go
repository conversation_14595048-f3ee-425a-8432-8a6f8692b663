package main

import "time"

// 获取配置
func getConfig() *Config {
	return &Config{
		// API配置
		BaseURL:  "https://abcyun.cn",
		DoctorID: "ffffffff00000000347efd26a343c000",
		ClinicID: "ffffffff00000000347efd3662de4002",
		
		// 请求头配置
		Headers: map[string]string{
			"host":             "abcyun.cn",
			"x-abc-ts":         "1751176339997",
			"user-agent":       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540512) XWEB/13871 Flue",
			"x-weapp-token":    "eyJhbGciOiJIUzI1NiJ9.eyJjaGFpbklkIjoiZmZmZmZmZmYwMDAwMDAwMDM0N2VmZDM2NjJkZTQwMDEiLCJhcHBJZCI6Ind4MzE1NjI0MWI0ZmM0YjdkMSIsImFwcFR5cGUiOjAsIm9wZW5JZCI6Im9GUmJ6NXRVTUN1c0hROGlJM2l3anVCTlpxR00iLCJ3eFVzZXJJZCI6IjM4MjAzMDEwNzEzMzI4ODQ0ODAiLCJ1bmlvbklkIjoib3VjWWg1Z1JXaExwNFpYWHZGWXpkaXpUaXQxVSIsImlzUENQcmV2aWV3IjowLCJpc0NoYWluRXhwaXJlZCI6MCwiaXNDaGFpbkVkaXRpb25NY0Rpc2FibGUiOjB9.56vvGHAU34EN6uLSDTJ0Eck0DXkhmZEn0uQaeYnxM0g",
			"accept":           "application/json, text/plain, */*",
			"x-abc-sign":       "1431f8376cfc002947bc95ef3327b3ba",
			"weapp-env":        "prod",
			"x-aua":            "ABCWeClinic/weh5-v2025.26.01",
			"weapp-app-vn":     "weh5-v2025.26.01",
			"weapp-app-id":     "wx3156241b4fc4b7d1",
			"sec-fetch-site":   "same-origin",
			"sec-fetch-mode":   "cors",
			"sec-fetch-dest":   "empty",
			"referer":          "https://abcyun.cn/mp/2182/doctor-detail?employeeId=ffffffff00000000347efd26a343c000&clinicId=ffffffff00000000347efd3662de4002&registrationDate=2025-06-29&tab=outpatient_registration",
			"accept-encoding":  "gzip, deflate, br",
			"accept-language":  "zh-CN,zh;q=0.9",
			"priority":         "u=1, i",
			"cookie":           "uuid=f9c5231af4e74e8599038d1a5b4de287; grayflag=",
		},
		
		// 监控配置
		MonitorDates:  []string{"2025-06-30", "2025-07-01"},
		CheckInterval: 5 * time.Second,
		
		// WxPusher配置 - 需要用户自己配置
		WxPusherToken: "YOUR_WXPUSHER_TOKEN",  // 需要替换为实际的token
		WxPusherUID:   "YOUR_WXPUSHER_UID",    // 需要替换为实际的UID
	}
}
