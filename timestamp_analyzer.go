package main

import (
	"fmt"
	"log"
	"strconv"
	"time"
)

// 分析时间戳
func analyzeTimestamp() {
	log.Println("=== 时间戳分析 ===")
	
	// 你的curl命令中的时间戳
	originalTimestamp := "1751176339997"
	
	// 解析原始时间戳
	if ts, err := strconv.ParseInt(originalTimestamp, 10, 64); err == nil {
		originalTime := time.Unix(ts/1000, (ts%1000)*1000000)
		log.Printf("原始时间戳: %s", originalTimestamp)
		log.Printf("对应时间: %s", originalTime.Format("2006-01-02 15:04:05.000"))
		log.Printf("时区: %s", originalTime.Location())
	}
	
	// 当前时间戳
	now := time.Now()
	currentTimestamp := now.UnixMilli()
	log.Printf("当前时间戳: %d", currentTimestamp)
	log.Printf("当前时间: %s", now.Format("2006-01-02 15:04:05.000"))
	
	// 时间差
	if ts, err := strconv.ParseInt(originalTimestamp, 10, 64); err == nil {
		diff := currentTimestamp - ts
		log.Printf("时间差: %d 毫秒 (%.2f 小时)", diff, float64(diff)/(1000*60*60))
	}
	
	// 生成一些测试时间戳
	log.Println("\n=== 测试时间戳生成 ===")
	
	// 方法1: 当前时间
	ts1 := time.Now().UnixMilli()
	log.Printf("方法1 - 当前时间: %d", ts1)
	
	// 方法2: 当前时间 + 偏移
	ts2 := time.Now().Add(8 * time.Hour).UnixMilli() // 假设需要时区偏移
	log.Printf("方法2 - 时区偏移: %d", ts2)
	
	// 方法3: 基于原始时间戳的模式
	if ts, err := strconv.ParseInt(originalTimestamp, 10, 64); err == nil {
		originalTime := time.Unix(ts/1000, (ts%1000)*1000000)
		// 假设需要保持相同的时间模式
		newTime := time.Now().In(originalTime.Location())
		ts3 := newTime.UnixMilli()
		log.Printf("方法3 - 保持时区: %d", ts3)
	}
}

// 测试不同的时间戳生成策略
func testTimestampStrategies() {
	log.Println("\n=== 时间戳策略测试 ===")
	
	strategies := map[string]func() int64{
		"当前UTC时间": func() int64 {
			return time.Now().UTC().UnixMilli()
		},
		"当前本地时间": func() int64 {
			return time.Now().UnixMilli()
		},
		"北京时间": func() int64 {
			loc, _ := time.LoadLocation("Asia/Shanghai")
			return time.Now().In(loc).UnixMilli()
		},
		"原始时间戳+偏移": func() int64 {
			// 基于原始时间戳，加上时间差
			original := int64(1751176339997)
			now := time.Now().UnixMilli()
			// 这里可以尝试不同的偏移策略
			return original + (now - original)
		},
	}
	
	for name, strategy := range strategies {
		ts := strategy()
		t := time.Unix(ts/1000, (ts%1000)*1000000)
		log.Printf("%s: %d (%s)", name, ts, t.Format("2006-01-02 15:04:05.000"))
	}
}

// 如果要运行时间戳分析，取消下面的注释
/*
func main() {
	analyzeTimestamp()
	testTimestampStrategies()
}
*/
