package main

import "time"

// 预约时段信息
type Shift struct {
	ID         string `json:"id"`
	Date       string `json:"date"`
	StartTime  string `json:"startTime"`
	EndTime    string `json:"endTime"`
	Status     int    `json:"status"`     // 1=约满，3=未放号，4=完诊
	StatusName string `json:"statusName"` // 约满、未放号、完诊
	DoctorName string `json:"doctorName"`
	ClinicName string `json:"clinicName"`
}

// API响应结构
type APIResponse struct {
	Code    int     `json:"code"`
	Message string  `json:"message"`
	Data    []Shift `json:"data"`
	Success bool    `json:"success"`
}

// 配置结构
type Config struct {
	// API相关配置
	BaseURL    string
	DoctorID   string
	ClinicID   string
	Headers    map[string]string
	
	// 监控配置
	MonitorDates []string
	CheckInterval time.Duration
	
	// WxPusher配置
	WxPusherToken string
	WxPusherUID   string
}

// 推送消息结构
type PushMessage struct {
	AppToken    string   `json:"appToken"`
	Content     string   `json:"content"`
	Summary     string   `json:"summary"`
	ContentType int      `json:"contentType"` // 1=文本，2=html，3=markdown
	TopicIds    []int    `json:"topicIds,omitempty"`
	UIDs        []string `json:"uids,omitempty"`
	URL         string   `json:"url,omitempty"`
}

// 推送响应
type PushResponse struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
	Data    []struct {
		Code   int    `json:"code"`
		Status string `json:"status"`
		UID    string `json:"uid"`
	} `json:"data"`
	Success bool `json:"success"`
}
