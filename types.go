package main

import "time"

// 预约时段信息
type Shift struct {
	ID         string `json:"id"`
	Date       string `json:"date"`
	StartTime  string `json:"startTime"`
	EndTime    string `json:"endTime"`
	Status     int    `json:"status"`     // 1=约满，3=未放号，4=完诊
	StatusName string `json:"statusName"` // 约满、未放号、完诊
	DoctorName string `json:"doctorName"`
	ClinicName string `json:"clinicName"`
}

// 实际的API响应结构
type APIResponse struct {
	Data struct {
		ReserveSectionDetails []ReserveSectionDetail `json:"reserveSectionDetails"`
	} `json:"data"`
}

type ReserveSectionDetail struct {
	Date       string   `json:"date"`
	DayOfWeek  string   `json:"dayOfWeek"`
	Status     int      `json:"status"`
	StatusName string   `json:"statusName"`
	Clinics    []Clinic `json:"clinics"`
}

type Clinic struct {
	ClinicID    string       `json:"clinicId"`
	ClinicName  string       `json:"clinicName"`
	Status      int          `json:"status"`
	StatusName  string       `json:"statusName"`
	Departments []Department `json:"departments"`
}

type Department struct {
	ID         string     `json:"id"`
	Name       string     `json:"name"`
	Status     int        `json:"status"`
	StatusName string     `json:"statusName"`
	RestCount  int        `json:"restCount"`
	DayShifts  []DayShift `json:"dayShifts"`
}

type DayShift struct {
	Shifts      []TimeSlot `json:"shifts"`
	RestCount   int        `json:"restCount"`
	TotalCount  int        `json:"totalCount"`
	Status      int        `json:"status"`
	StatusName  string     `json:"statusName"`
	Start       string     `json:"start"`
	End         string     `json:"end"`
	TimeOfDay   string     `json:"timeOfDay"`
}

type TimeSlot struct {
	OrderNo     int    `json:"orderNo"`
	Start       string `json:"start"`
	End         string `json:"end"`
	TimeOfDay   string `json:"timeOfDay"`
	Available   int    `json:"available"`
	Count       int    `json:"count"`
	TotalCount  int    `json:"totalCount"`
}

// 配置结构
type Config struct {
	// API相关配置
	BaseURL    string
	DoctorID   string
	ClinicID   string
	Headers    map[string]string
	
	// 监控配置
	MonitorDates []string
	CheckInterval time.Duration
	
	// WxPusher配置
	WxPusherToken string
	WxPusherUID   string
}

// 推送消息结构
type PushMessage struct {
	AppToken    string   `json:"appToken"`
	Content     string   `json:"content"`
	Summary     string   `json:"summary"`
	ContentType int      `json:"contentType"` // 1=文本，2=html，3=markdown
	TopicIds    []int    `json:"topicIds,omitempty"`
	UIDs        []string `json:"uids,omitempty"`
	URL         string   `json:"url,omitempty"`
}

// 推送响应
type PushResponse struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
	Data    []struct {
		Code   int    `json:"code"`
		Status string `json:"status"`
		UID    string `json:"uid"`
	} `json:"data"`
	Success bool `json:"success"`
}
