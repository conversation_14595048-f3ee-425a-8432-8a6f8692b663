package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	log.Println("=== 中医预约抢号系统 ===")

	// 加载配置
	config := getConfig()

	// 检查WxPusher配置
	if config.WxPusherToken == "YOUR_WXPUSHER_TOKEN" {
		log.Println("⚠️  警告: WxPusher未配置，将不会发送推送通知")
		log.Println("请在config.go中配置WxPusherToken和WxPusherUID")
	}

	// 创建监控器
	monitor := NewMonitor(config)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动监控
	go monitor.Start()

	// 等待退出信号
	<-sigChan
	log.Println("收到退出信号，正在关闭...")
}
