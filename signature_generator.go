package main

import (
	"crypto/md5"
	"fmt"
	"log"
	"strconv"
	"time"
)

// 签名生成器 - 尝试逆向分析签名算法
type SignatureGenerator struct {
	// 可能的密钥
	possibleKeys []string
}

func NewSignatureGenerator() *SignatureGenerator {
	return &SignatureGenerator{
		possibleKeys: []string{
			"abc",
			"abcyun",
			"weapp",
			"wx3156241b4fc4b7d1",
			"secret",
			"key",
			"ABCWeClinic",
			"ffffffff00000000347efd3662de4002", // clinicId
			"ffffffff00000000347efd26a343c000", // doctorId
		},
	}
}

// 生成MD5哈希
func (sg *SignatureGenerator) md5Hash(input string) string {
	hash := md5.Sum([]byte(input))
	return fmt.Sprintf("%x", hash)
}

// 测试各种签名生成方式
func (sg *SignatureGenerator) testSignatureGeneration(timestamp string, expectedSign string) {
	log.Printf("=== 测试时间戳: %s ===", timestamp)
	log.Printf("期望签名: %s", expectedSign)
	log.Println()

	// 方式1: 纯时间戳
	sign1 := sg.md5Hash(timestamp)
	log.Printf("方式1 - md5(timestamp): %s %s", sign1, sg.match(sign1, expectedSign))

	// 方式2: 时间戳 + 固定密钥
	for _, key := range sg.possibleKeys {
		sign := sg.md5Hash(timestamp + key)
		log.Printf("方式2 - md5(timestamp + '%s'): %s %s", key, sign, sg.match(sign, expectedSign))
		
		sign = sg.md5Hash(key + timestamp)
		log.Printf("方式2 - md5('%s' + timestamp): %s %s", key, sign, sg.match(sign, expectedSign))
	}

	// 方式3: 时间戳的变换
	if ts, err := strconv.ParseInt(timestamp, 10, 64); err == nil {
		// 时间戳除以1000
		ts_sec := ts / 1000
		sign := sg.md5Hash(strconv.FormatInt(ts_sec, 10))
		log.Printf("方式3 - md5(timestamp/1000): %s %s", sign, sg.match(sign, expectedSign))
		
		// 时间戳的字符串操作
		sign = sg.md5Hash(timestamp[:10]) // 前10位
		log.Printf("方式3 - md5(timestamp[:10]): %s %s", sign, sg.match(sign, expectedSign))
	}

	// 方式4: 包含其他参数
	clinicId := "ffffffff00000000347efd3662de4002"
	doctorId := "ffffffff00000000347efd26a343c000"
	userGps := "120.31790161132812,31.561315536499023"

	sign := sg.md5Hash(timestamp + clinicId)
	log.Printf("方式4 - md5(timestamp + clinicId): %s %s", sign, sg.match(sign, expectedSign))

	sign = sg.md5Hash(timestamp + doctorId)
	log.Printf("方式4 - md5(timestamp + doctorId): %s %s", sign, sg.match(sign, expectedSign))

	sign = sg.md5Hash(timestamp + clinicId + doctorId)
	log.Printf("方式4 - md5(timestamp + clinicId + doctorId): %s %s", sign, sg.match(sign, expectedSign))

	// 方式5: 包含URL参数
	params := "t=" + timestamp + "&clinicId=" + clinicId + "&registrationType=0&userGps=" + userGps
	sign = sg.md5Hash(params)
	log.Printf("方式5 - md5(url_params): %s %s", sign, sg.match(sign, expectedSign))

	sign = sg.md5Hash(timestamp + params)
	log.Printf("方式5 - md5(timestamp + url_params): %s %s", sign, sg.match(sign, expectedSign))

	// 方式6: 尝试不同的组合顺序
	sign = sg.md5Hash(clinicId + timestamp + doctorId)
	log.Printf("方式6 - md5(clinicId + timestamp + doctorId): %s %s", sign, sg.match(sign, expectedSign))

	sign = sg.md5Hash(doctorId + timestamp + clinicId)
	log.Printf("方式6 - md5(doctorId + timestamp + clinicId): %s %s", sign, sg.match(sign, expectedSign))

	log.Println()
}

// 检查是否匹配
func (sg *SignatureGenerator) match(generated, expected string) string {
	if generated == expected {
		return "✅ 匹配!"
	}
	return "❌"
}

// 分析已知的签名样本
func analyzeSignatures() {
	log.Println("=== X-ABC-Sign 签名分析 ===")
	
	generator := NewSignatureGenerator()
	
	// 已知的三个样本
	samples := []struct {
		timestamp string
		signature string
	}{
		{"1751176339997", "1431f8376cfc002947bc95ef3327b3ba"},
		{"1751179074189", "785485f303260fa625381e148a4d2e22"},
		{"1751179534514", "12f9e5bcc83cf58298a0ce51b22ecc45"},
	}
	
	for _, sample := range samples {
		generator.testSignatureGeneration(sample.timestamp, sample.signature)
	}
	
	// 如果找到规律，生成当前时间的签名
	log.Println("=== 生成当前时间签名 ===")
	currentTime := strconv.FormatInt(time.Now().UnixMilli(), 10)
	log.Printf("当前时间戳: %s", currentTime)
	
	// 这里可以根据找到的规律生成签名
	// 例如：如果发现规律是 md5(timestamp + "abc")
	// currentSign := generator.md5Hash(currentTime + "abc")
	// log.Printf("生成的签名: %s", currentSign)
}

// 如果要运行签名分析，取消下面的注释
/*
func main() {
	analyzeSignatures()
}
*/
