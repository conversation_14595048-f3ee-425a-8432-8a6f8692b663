# 快速配置指南

## 第一步：配置WxPusher推送服务

### 1. 注册WxPusher账号
- 访问：https://wxpusher.zjiecode.com/
- 注册账号并登录

### 2. 创建应用
- 点击"应用管理" -> "新建应用"
- 填写应用名称（如：中医预约监控）
- 记录下生成的 **AppToken**

### 3. 获取用户UID
- 用微信扫描应用的二维码关注
- 在"用户管理"中找到你的 **UID**

### 4. 配置系统
在 `config.go` 文件中找到以下两行：
```go
WxPusherToken: "YOUR_WXPUSHER_TOKEN",  // 替换为你的AppToken
WxPusherUID:   "YOUR_WXPUSHER_UID",    // 替换为你的UID
```

将 `YOUR_WXPUSHER_TOKEN` 替换为你的AppToken
将 `YOUR_WXPUSHER_UID` 替换为你的UID

## 第二步：更新认证信息（如果需要）

如果系统提示认证失败，可能需要更新以下信息：

### 1. 获取新的token
- 打开微信小程序，进入预约页面
- 使用浏览器开发者工具抓取请求
- 更新 `config.go` 中的 `x-weapp-token` 和 `x-abc-sign`

### 2. 更新时间戳
- 将 `x-abc-ts` 更新为当前时间戳

## 第三步：运行系统

### 方法1：使用批处理文件
双击 `start.bat` 文件

### 方法2：使用命令行
```bash
go run .
```

### 方法3：使用编译后的程序
```bash
./zhongyi_yuyue.exe
```

## 第四步：测试系统

如果想要测试系统是否正常工作：

1. 修改 `test.go` 文件，取消最后的注释
2. 运行测试：`go run test.go`

## 常见问题

### Q: 提示"WxPusher未配置"
A: 请按照第一步配置WxPusher

### Q: 提示"API返回错误"或"请求失败"
A: 可能是认证信息过期，请按照第二步更新认证信息

### Q: 收不到推送消息
A: 检查WxPusher配置是否正确，确保已关注应用的微信公众号

### Q: 想要监控其他日期
A: 修改 `config.go` 中的 `MonitorDates` 数组

### Q: 想要调整检查频率
A: 修改 `config.go` 中的 `CheckInterval`（建议不要低于3秒）

## 注意事项

1. 请保持网络连接稳定
2. 不要设置过于频繁的检查间隔，避免对服务器造成压力
3. 妥善保管你的认证token和WxPusher配置
4. 如果长时间运行，建议定期检查认证信息是否过期
