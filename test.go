package main

import (
	"fmt"
	"log"
)

// 测试函数 - 可以用来验证配置和网络连接
func testSystem() {
	log.Println("=== 系统测试 ===")
	
	// 加载配置
	config := getConfig()
	
	// 测试配置
	log.Printf("监控日期: %v", config.MonitorDates)
	log.Printf("检查间隔: %v", config.CheckInterval)
	log.Printf("WxPusher配置状态: %s", 
		func() string {
			if config.WxPusherToken == "YOUR_WXPUSHER_TOKEN" {
				return "未配置"
			}
			return "已配置"
		}())
	
	// 创建监控器
	monitor := NewMonitor(config)
	
	// 测试API连接
	log.Println("测试API连接...")
	for _, date := range config.MonitorDates {
		shifts, err := monitor.fetchShifts(date)
		if err != nil {
			log.Printf("❌ %s API测试失败: %v", date, err)
		} else {
			log.Printf("✅ %s API测试成功，获取到%d个时段", date, len(shifts))
			
			// 显示前3个时段的详细信息
			for i, shift := range shifts {
				if i >= 3 {
					break
				}
				log.Printf("   时段%d: %s-%s, 状态:%d(%s)", 
					i+1, shift.StartTime, shift.EndTime, shift.Status, shift.StatusName)
			}
		}
	}
	
	// 测试WxPusher连接
	if config.WxPusherToken != "YOUR_WXPUSHER_TOKEN" {
		log.Println("测试WxPusher连接...")
		pusher := NewWxPusher(config)
		err := pusher.SendMessage("🧪 这是一条测试消息", "系统测试")
		if err != nil {
			log.Printf("❌ WxPusher测试失败: %v", err)
		} else {
			log.Println("✅ WxPusher测试成功")
		}
	} else {
		log.Println("⚠️  跳过WxPusher测试（未配置）")
	}
	
	log.Println("=== 测试完成 ===")
}

// 如果要运行测试，取消下面的注释并注释掉main函数中的monitor.Start()
/*
func main() {
	testSystem()
}
*/
