package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/go-resty/resty/v2"
)

// WxPusher推送服务
type WxPusher struct {
	client *resty.Client
	config *Config
}

// 创建新的WxPusher实例
func NewWxPusher(config *Config) *WxPusher {
	client := resty.New()
	client.SetTimeout(10 * time.Second)
	
	return &WxPusher{
		client: client,
		config: config,
	}
}

// 发送推送消息
func (w *WxPusher) SendMessage(content, summary string) error {
	if w.config.WxPusherToken == "YOUR_WXPUSHER_TOKEN" {
		log.Printf("WxPusher未配置，跳过推送: %s", summary)
		return nil
	}

	message := PushMessage{
		AppToken:    w.config.WxPusherToken,
		Content:     content,
		Summary:     summary,
		ContentType: 1, // 文本消息
		UIDs:        []string{w.config.WxPusherUID},
	}

	resp, err := w.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(message).
		Post("https://wxpusher.zjiecode.com/api/send/message")

	if err != nil {
		return fmt.Errorf("发送推送失败: %v", err)
	}

	var pushResp PushResponse
	if err := json.Unmarshal(resp.Body(), &pushResp); err != nil {
		return fmt.Errorf("解析推送响应失败: %v", err)
	}

	if !pushResp.Success {
		return fmt.Errorf("推送失败: %s", pushResp.Message)
	}

	log.Printf("推送成功: %s", summary)
	return nil
}

// 发送可预约通知
func (w *WxPusher) SendAvailableNotification(shifts []Shift) error {
	if len(shifts) == 0 {
		return nil
	}

	content := "🎉 发现可预约的号源！\n\n"
	for _, shift := range shifts {
		content += fmt.Sprintf("📅 日期: %s\n", shift.Date)
		content += fmt.Sprintf("⏰ 时间: %s - %s\n", shift.StartTime, shift.EndTime)
		content += fmt.Sprintf("👨‍⚕️ 医生: %s\n", shift.DoctorName)
		content += fmt.Sprintf("🏥 诊所: %s\n", shift.ClinicName)
		content += fmt.Sprintf("📊 状态: %s\n", shift.StatusName)
		content += "---\n"
	}

	summary := fmt.Sprintf("发现%d个可预约号源", len(shifts))
	return w.SendMessage(content, summary)
}

// 发送状态报告
func (w *WxPusher) SendStatusReport(date string, shifts []Shift) error {
	availableCount := 0
	fullCount := 0
	notReleasedCount := 0
	finishedCount := 0

	for _, shift := range shifts {
		switch shift.Status {
		case 1: // 约满
			fullCount++
		case 3: // 未放号
			notReleasedCount++
		case 4: // 完诊
			finishedCount++
		default: // 可预约
			availableCount++
		}
	}

	content := fmt.Sprintf("📊 %s 预约状态报告\n\n", date)
	content += fmt.Sprintf("✅ 可预约: %d个\n", availableCount)
	content += fmt.Sprintf("❌ 约满: %d个\n", fullCount)
	content += fmt.Sprintf("⏳ 未放号: %d个\n", notReleasedCount)
	content += fmt.Sprintf("✔️ 完诊: %d个\n", finishedCount)
	content += fmt.Sprintf("📈 总计: %d个时段\n", len(shifts))

	summary := fmt.Sprintf("%s状态报告", date)
	return w.SendMessage(content, summary)
}
