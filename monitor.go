package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
)

// 监控器
type Monitor struct {
	client  *resty.Client
	config  *Config
	pusher  *WxPusher
	lastStatus map[string][]Shift // 记录上次的状态，用于对比变化
}

// 创建新的监控器
func NewMonitor(config *Config) *Monitor {
	client := resty.New()
	client.SetTimeout(10 * time.Second)
	
	return &Monitor{
		client:     client,
		config:     config,
		pusher:     NewWxPusher(config),
		lastStatus: make(map[string][]Shift),
	}
}

// 获取指定日期的预约信息
func (m *Monitor) fetchShifts(date string) ([]Shift, error) {
	// 构建请求URL
	url := fmt.Sprintf("%s/api-weapp/registration/chain/doctor/%s/shifts", 
		m.config.BaseURL, m.config.DoctorID)
	
	// 生成时间戳 - x-abc-ts 和 t 参数必须相同
	currentTime := strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 构建查询参数 - 基于最新curl命令
	params := map[string]string{
		"t":                currentTime,
		"clinicId":         m.config.ClinicID,
		"registrationType": "0",
		"userGps":          "120.31790161132812,31.561315536499023", // GPS坐标
	}

	// 复制请求头并更新时间戳和referer
	headers := make(map[string]string)
	for k, v := range m.config.Headers {
		headers[k] = v
	}
	// 关键：x-abc-ts 必须与 URL参数 t 相同
	headers["x-abc-ts"] = currentTime

	// 更新referer中的日期
	baseReferer := "https://abcyun.cn/mp/2182/doctor-detail?employeeId=ffffffff00000000347efd26a343c000&clinicId=ffffffff00000000347efd3662de4002&registrationDate="
	headers["referer"] = baseReferer + date + "&tab=outpatient_registration"

	log.Printf("请求URL: %s", url)
	log.Printf("请求参数: %+v", params)
	log.Printf("时间戳同步: x-abc-ts=%s, t=%s", headers["x-abc-ts"], params["t"])

	// 发送请求
	resp, err := m.client.R().
		SetHeaders(headers).
		SetQueryParams(params).
		Get(url)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 打印响应状态和内容用于调试
	log.Printf("API响应状态: %d", resp.StatusCode())
	log.Printf("API响应内容: %s", string(resp.Body()))

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(resp.Body(), &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v, 响应内容: %s", err, string(resp.Body()))
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("API返回错误: %s (code: %d)", apiResp.Message, apiResp.Code)
	}

	// 为每个时段补充日期信息
	for i := range apiResp.Data {
		apiResp.Data[i].Date = date
	}

	return apiResp.Data, nil
}

// 检查是否有可预约的号
func (m *Monitor) checkAvailableShifts(shifts []Shift) []Shift {
	var available []Shift
	for _, shift := range shifts {
		// status不是1(约满)、3(未放号)、4(完诊)的都认为是可预约的
		if shift.Status != 1 && shift.Status != 3 && shift.Status != 4 {
			available = append(available, shift)
		}
	}
	return available
}

// 比较状态变化
func (m *Monitor) compareStatus(date string, newShifts []Shift) []Shift {
	lastShifts, exists := m.lastStatus[date]
	if !exists {
		// 第一次检查，记录状态
		m.lastStatus[date] = newShifts
		return m.checkAvailableShifts(newShifts)
	}

	// 创建上次状态的映射
	lastStatusMap := make(map[string]int)
	for _, shift := range lastShifts {
		key := fmt.Sprintf("%s-%s", shift.StartTime, shift.EndTime)
		lastStatusMap[key] = shift.Status
	}

	// 检查新出现的可预约号
	var newAvailable []Shift
	for _, shift := range newShifts {
		key := fmt.Sprintf("%s-%s", shift.StartTime, shift.EndTime)
		lastStatus, exists := lastStatusMap[key]
		
		// 如果之前不存在或者状态从不可预约变为可预约
		if !exists || (lastStatus == 1 || lastStatus == 3 || lastStatus == 4) && 
		   (shift.Status != 1 && shift.Status != 3 && shift.Status != 4) {
			newAvailable = append(newAvailable, shift)
		}
	}

	// 更新状态
	m.lastStatus[date] = newShifts
	return newAvailable
}

// 执行单次检查
func (m *Monitor) checkOnce() {
	log.Println("开始检查预约状态...")
	
	for _, date := range m.config.MonitorDates {
		log.Printf("检查日期: %s", date)
		
		shifts, err := m.fetchShifts(date)
		if err != nil {
			log.Printf("获取%s的预约信息失败: %v", date, err)
			continue
		}

		log.Printf("获取到%d个时段", len(shifts))

		// 检查新的可预约号
		newAvailable := m.compareStatus(date, shifts)
		if len(newAvailable) > 0 {
			log.Printf("发现%d个新的可预约号", len(newAvailable))
			if err := m.pusher.SendAvailableNotification(newAvailable); err != nil {
				log.Printf("发送可预约通知失败: %v", err)
			}
		}

		// 打印当前状态统计
		available := m.checkAvailableShifts(shifts)
		log.Printf("%s - 可预约: %d, 总计: %d", date, len(available), len(shifts))
	}
}

// 开始监控
func (m *Monitor) Start() {
	log.Printf("开始监控，检查间隔: %v", m.config.CheckInterval)
	log.Printf("监控日期: %v", m.config.MonitorDates)
	
	// 发送启动通知
	if err := m.pusher.SendMessage("🚀 中医预约监控系统已启动", "监控启动"); err != nil {
		log.Printf("发送启动通知失败: %v", err)
	}

	// 立即执行一次检查
	m.checkOnce()

	// 定时检查
	ticker := time.NewTicker(m.config.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.checkOnce()
		}
	}
}
