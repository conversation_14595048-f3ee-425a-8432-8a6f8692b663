@echo off
chcp 65001 >nul
echo ================================
echo    测试原始curl命令
echo ================================
echo.

echo 正在测试你的最新curl命令...
echo.

curl "https://abcyun.cn/api-weapp/registration/chain/doctor/ffffffff00000000347efd26a343c000/shifts?t=1751179074189&clinicId=ffffffff00000000347efd3662de4002&registrationType=0&userGps=120.31790161132812,31.561315536499023" ^
  -H "Connection: keep-alive" ^
  -H "x-weapp-token: eyJhbGciOiJIUzI1NiJ9.eyJjaGFpbklkIjoiZmZmZmZmZmYwMDAwMDAwMDM0N2VmZDM2NjJkZTQwMDEiLCJwYXRpZW50SWQiOiJmZmZmZmZmZjAwMDAwMDAwMzQ4NTFjNTczNGRkMDAwMCIsImFwcElkIjoid3gzMTU2MjQxYjRmYzRiN2QxIiwiYXBwVHlwZSI6MCwib3BlbklkIjoib0ZSYno1dFVNQ3VzSFE4aUkzaXdqdUJOWnFHTSIsInd4VXNlcklkIjoiMzgyMDMwMTA3MTMzMjg4NDQ4MCIsInVuaW9uSWQiOiJvdWNZaDVnUldoTHA0WlhYdkZZemRpelRpdDFVIiwiaXNQQ1ByZXZpZXciOjAsImlzQ2hhaW5FeHBpcmVkIjowLCJpc0NoYWluRWRpdGlvbk1jRGlzYWJsZSI6MH0.22Et0VUrCiSj5c7vm06qGq5siuz23m4wYccOHZ9gHks" ^
  -H "Accept: application/json, text/plain, */*" ^
  -H "Sec-Fetch-Site: same-origin" ^
  -H "X-ABC-Sign: 785485f303260fa625381e148a4d2e22" ^
  -H "X-ABC-Ts: 1751179074189" ^
  -H "Accept-Language: zh-CN,zh-Hans;q=0.9" ^
  -H "Sec-Fetch-Mode: cors" ^
  -H "Cookie: grayflag=; uuid=5368cbbb3e97458aa5201621902a0008" ^
  -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.60(0x18003c30) NetType/WIFI Language/zh_CN" ^
  -H "weapp-env: prod" ^
  -H "weapp-app-id: wx3156241b4fc4b7d1" ^
  -H "Referer: https://abcyun.cn/mp/2182/doctor-detail?employeeId=ffffffff00000000347efd26a343c000&clinicId=ffffffff00000000347efd3662de4002&registrationDate=2025-06-30&tab=outpatient_registration" ^
  -H "X-AUA: ABCWeClinic/weh5-v2025.26.01" ^
  -H "Sec-Fetch-Dest: empty" ^
  -H "weapp-app-vn: weh5-v2025.26.01"

echo.
echo ================================
echo 测试完成
echo ================================
pause
