# 签名分析报告

## 问题分析

通过对比你提供的两个curl命令，发现关键问题：**X-ABC-Sign签名是动态生成的**

### 签名对比

| 时间戳 | X-ABC-Sign | 
|--------|------------|
| 1751176339997 | 1431f8376cfc002947bc95ef3327b3ba |
| 1751179074189 | 785485f303260fa625381e148a4d2e22 |

**结论**: 签名完全不同，说明它是基于某种算法动态计算的。

## 可能的签名生成方式

### 方式1: 基于时间戳的MD5/SHA1
```
sign = md5(timestamp + secret_key)
sign = md5("1751179074189" + "secret")
```

### 方式2: 基于请求参数的签名
```
sign = md5(sorted_params + timestamp + secret)
```

### 方式3: 基于完整请求的签名
```
sign = md5(method + url + params + timestamp + secret)
```

## 解决方案

### 方案1: 使用固定签名（临时方案）
暂时使用你最新curl命令中的签名，看看是否还有其他问题：

```go
"X-ABC-Sign": "785485f303260fa625381e148a4d2e22",
```

### 方案2: 分析签名规律（推荐）
需要你提供更多的curl命令样本来分析规律。

### 方案3: 逆向分析
分析微信小程序的JavaScript代码，找到签名生成算法。

## 当前状态

我已经更新了所有其他配置：
- ✅ 时间戳同步 (X-ABC-Ts = t参数)
- ✅ GPS坐标
- ✅ 新的token
- ✅ 新的User-Agent
- ✅ 新的Cookie
- ❌ 签名仍然是静态的

## 下一步测试

让我们先测试一下你的原始curl命令是否还能工作：

```bash
# 测试你的最新curl命令
curl 'https://abcyun.cn/api-weapp/registration/chain/doctor/ffffffff00000000347efd26a343c000/shifts?t=1751179074189&clinicId=ffffffff00000000347efd3662de4002&registrationType=0&userGps=120.31790161132812,31.561315536499023' \
  -H 'X-ABC-Sign: 785485f303260fa625381e148a4d2e22' \
  -H 'X-ABC-Ts: 1751179074189' \
  [其他头...]
```

如果原始命令失败，说明签名确实有时效性。
如果原始命令成功，我们需要找出Go代码中的其他差异。

## 临时解决方案

我可以创建一个"签名更新提醒"功能：
1. 当检测到403错误时，提醒你更新签名
2. 提供简单的配置更新方法
3. 记录签名的有效时间，估算更新频率
