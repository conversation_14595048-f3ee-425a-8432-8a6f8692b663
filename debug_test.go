package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
)

// 调试测试函数
func debugAPI() {
	log.Println("=== API调试测试 ===")
	
	config := getConfig()
	client := resty.New()
	client.SetTimeout(10 * time.Second)
	
	// 测试URL
	url := fmt.Sprintf("%s/api-weapp/registration/chain/doctor/%s/shifts", 
		config.BaseURL, config.DoctorID)
	
	// 当前时间戳
	currentTime := strconv.FormatInt(time.Now().UnixMilli(), 10)
	
	// 测试参数
	params := map[string]string{
		"t":                currentTime,
		"clinicId":         config.ClinicID,
		"registrationType": "0",
		"userGps":          "",
	}
	
	// 更新时间戳
	headers := make(map[string]string)
	for k, v := range config.Headers {
		headers[k] = v
	}
	headers["x-abc-ts"] = currentTime
	
	log.Printf("请求URL: %s", url)
	log.Printf("请求参数: %+v", params)
	log.Printf("请求头数量: %d", len(headers))
	
	// 打印关键请求头
	log.Printf("关键请求头:")
	log.Printf("  x-weapp-token: %s", headers["x-weapp-token"][:20]+"...")
	log.Printf("  x-abc-sign: %s", headers["x-abc-sign"])
	log.Printf("  x-abc-ts: %s", headers["x-abc-ts"])
	log.Printf("  cookie: %s", headers["cookie"])
	
	// 发送请求
	resp, err := client.R().
		SetHeaders(headers).
		SetQueryParams(params).
		Get(url)
	
	if err != nil {
		log.Printf("❌ 请求失败: %v", err)
		return
	}
	
	log.Printf("响应状态码: %d", resp.StatusCode())
	log.Printf("响应头: %+v", resp.Header())
	log.Printf("响应内容: %s", string(resp.Body()))
	
	// 尝试解析响应
	if resp.StatusCode() == 200 {
		var apiResp APIResponse
		if err := json.Unmarshal(resp.Body(), &apiResp); err != nil {
			log.Printf("解析响应失败: %v", err)
		} else {
			log.Printf("✅ API调用成功")
			log.Printf("Success: %v", apiResp.Success)
			log.Printf("Message: %s", apiResp.Message)
			log.Printf("Data count: %d", len(apiResp.Data))
		}
	} else {
		log.Printf("❌ API调用失败，状态码: %d", resp.StatusCode())
	}
}

// 如果要运行调试测试，取消下面的注释
/*
func main() {
	debugAPI()
}
*/
