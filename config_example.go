package main

// 这是一个配置示例文件
// 复制这个文件为 config_custom.go 并修改其中的配置

/*
func getCustomConfig() *Config {
	return &Config{
		// API配置 - 通常不需要修改
		BaseURL:  "https://abcyun.cn",
		DoctorID: "ffffffff00000000347efd26a343c000",
		ClinicID: "ffffffff00000000347efd3662de4002",
		
		// 请求头配置 - 如果token过期需要更新
		Headers: map[string]string{
			"host":             "abcyun.cn",
			"x-abc-ts":         "1751176339997",
			"user-agent":       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540512) XWEB/13871 Flue",
			"x-weapp-token":    "你的实际token",
			"accept":           "application/json, text/plain, */*",
			"x-abc-sign":       "你的实际签名",
			"weapp-env":        "prod",
			"x-aua":            "ABCWeClinic/weh5-v2025.26.01",
			"weapp-app-vn":     "weh5-v2025.26.01",
			"weapp-app-id":     "wx3156241b4fc4b7d1",
			"sec-fetch-site":   "same-origin",
			"sec-fetch-mode":   "cors",
			"sec-fetch-dest":   "empty",
			"referer":          "https://abcyun.cn/mp/2182/doctor-detail?employeeId=ffffffff00000000347efd26a343c000&clinicId=ffffffff00000000347efd3662de4002&registrationDate=2025-06-29&tab=outpatient_registration",
			"accept-encoding":  "gzip, deflate, br",
			"accept-language":  "zh-CN,zh;q=0.9",
			"priority":         "u=1, i",
			"cookie":           "uuid=f9c5231af4e74e8599038d1a5b4de287; grayflag=",
		},
		
		// 监控配置 - 可以根据需要修改
		MonitorDates:  []string{"2025-06-30", "2025-07-01", "2025-07-02"}, // 添加更多日期
		CheckInterval: 5 * time.Second, // 检查间隔，可以调整为3秒、10秒等
		
		// WxPusher配置 - 必须配置
		WxPusherToken: "AT_xxxxxxxxxxxxxxxxxxxxxxxxxx", // 你的WxPusher应用token
		WxPusherUID:   "UID_xxxxxxxxxxxxxxxxxxxxxxxxx",  // 你的WxPusher用户UID
	}
}
*/

// WxPusher配置说明：
// 1. 访问 https://wxpusher.zjiecode.com/
// 2. 注册账号并创建应用
// 3. 获取应用的AppToken
// 4. 关注应用的微信公众号获取UID
// 5. 将AppToken和UID填入上面的配置中
