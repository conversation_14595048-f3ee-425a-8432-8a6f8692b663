# 获取最新认证信息指南

## 问题分析

当前API返回403错误，错误信息为"Invalid request"，这表明认证信息已过期或不正确。

## 解决方案

### 方法1：使用浏览器开发者工具获取最新认证信息

1. **打开微信小程序**
   - 在微信中打开中医预约小程序
   - 进入医生预约页面

2. **打开开发者工具**
   - 在电脑上打开微信开发者工具
   - 或者使用微信PC版的调试功能

3. **抓取网络请求**
   - 在开发者工具中打开"Network"标签
   - 刷新预约页面或切换日期
   - 找到类似这样的请求：
     ```
     https://abcyun.cn/api-weapp/registration/chain/doctor/ffffffff00000000347efd26a343c000/shifts?t=...&clinicId=...
     ```

4. **复制请求信息**
   - 右键点击该请求
   - 选择"Copy as cURL"
   - 将复制的内容发送给我，我会帮你更新配置

### 方法2：手动更新关键认证信息

在 `config.go` 文件中更新以下关键字段：

```go
"x-weapp-token": "你的最新token",
"x-abc-sign": "你的最新签名",
"x-abc-ts": "当前时间戳",
"cookie": "你的最新cookie",
```

### 方法3：使用抓包工具

1. 使用Fiddler、Charles或类似工具
2. 配置手机代理
3. 在手机微信中操作预约页面
4. 抓取HTTPS请求
5. 获取最新的请求头信息

## 常见的认证字段说明

- `x-weapp-token`: 微信小程序认证token，通常以"eyJ"开头
- `x-abc-sign`: API签名，用于验证请求合法性
- `x-abc-ts`: 时间戳，用于防重放攻击
- `cookie`: 会话cookie，包含用户身份信息

## 注意事项

1. 认证信息通常有时效性，可能需要定期更新
2. 不同的操作可能需要不同的签名
3. 时间戳需要与服务器时间同步
4. 请求头的顺序和格式要与原始请求保持一致

## 测试方法

更新配置后，可以运行以下命令测试：

```bash
go run .
```

如果仍然出现403错误，请提供最新的curl命令，我会帮你进一步调试。
