# 中医预约抢号系统

一个用Go语言编写的中医预约平台监控系统，能够自动监控指定日期的预约状态，并在发现可预约号源时通过WxPusher推送通知。

## 功能特性

- 🔍 **实时监控**: 每5秒检查一次预约状态
- 📅 **多日期监控**: 同时监控多个日期的预约情况
- 📱 **即时推送**: 发现可预约号源时立即推送通知
- 📊 **状态统计**: 提供详细的预约状态统计信息
- 🔄 **状态变化检测**: 智能检测状态变化，避免重复推送

## 预约状态说明

- `status = 1`: 约满 (statusName: "约满")
- `status = 3`: 未放号 (statusName: "未放号") 
- `status = 4`: 完诊 (statusName: "完诊")
- 其他状态: 可预约

## 安装和使用

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置WxPusher

在 `config.go` 文件中配置你的WxPusher信息：

```go
// WxPusher配置
WxPusherToken: "YOUR_ACTUAL_WXPUSHER_TOKEN",  // 替换为你的token
WxPusherUID:   "YOUR_ACTUAL_WXPUSHER_UID",    // 替换为你的UID
```

### 3. 运行程序

```bash
go run .
```

## 配置说明

### API配置
- `BaseURL`: API基础地址
- `DoctorID`: 医生ID
- `ClinicID`: 诊所ID
- `Headers`: 请求头信息（包含认证token等）

### 监控配置
- `MonitorDates`: 要监控的日期列表，默认为 `["2025-06-30", "2025-07-01"]`
- `CheckInterval`: 检查间隔，默认为5秒

### WxPusher配置
- `WxPusherToken`: WxPusher应用token
- `WxPusherUID`: 接收推送的用户UID

## 推送消息类型

1. **启动通知**: 系统启动时发送
2. **可预约通知**: 发现新的可预约号源时发送
3. **状态报告**: 定期发送各日期的状态统计

## 注意事项

1. 请确保网络连接稳定
2. 请合理设置检查间隔，避免对服务器造成过大压力
3. 请妥善保管你的认证token和WxPusher配置信息
4. 建议在使用前先测试WxPusher推送功能是否正常

## 文件结构

```
.
├── main.go      # 主程序入口
├── config.go    # 配置文件
├── monitor.go   # 监控逻辑
├── pusher.go    # 推送服务
├── types.go     # 数据结构定义
├── go.mod       # 依赖管理
└── README.md    # 说明文档
```

## 依赖包

- `github.com/go-resty/resty/v2`: HTTP客户端
- `github.com/robfig/cron/v3`: 定时任务（预留）

## 许可证

MIT License
